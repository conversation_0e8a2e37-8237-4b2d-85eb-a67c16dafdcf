import { useEffect, useRef, useState } from 'react';

interface ScrollRevealProps {
  children: React.ReactNode;
  delay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  distance?: number;
  duration?: number;
  className?: string;
}

/**
 * Simple, reliable scroll reveal component
 * Guaranteed to work with visible animations
 */
export const ScrollReveal = ({
  children,
  delay = 0,
  direction = 'up',
  distance = 50,
  duration = 500, // Reduced from 800ms to 500ms for faster animations
  className = ''
}: ScrollRevealProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            setIsVisible(true);
          }, delay);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [delay]);

  const getTransform = () => {
    if (isVisible) return 'translate3d(0, 0, 0) scale(1)';

    switch (direction) {
      case 'up':
        return `translate3d(0, ${distance}px, 0) scale(0.95)`;
      case 'down':
        return `translate3d(0, -${distance}px, 0) scale(0.95)`;
      case 'left':
        return `translate3d(${distance}px, 0, 0) scale(0.95)`;
      case 'right':
        return `translate3d(-${distance}px, 0, 0) scale(0.95)`;
      default:
        return `translate3d(0, ${distance}px, 0) scale(0.95)`;
    }
  };

  return (
    <div
      ref={elementRef}
      className={className}
      style={{
        opacity: isVisible ? 1 : 0,
        transform: getTransform(),
        transition: `all ${duration}ms cubic-bezier(0.16, 1, 0.3, 1)`,
        willChange: 'transform, opacity'
      }}
    >
      {children}
    </div>
  );
};

interface StaggeredRevealProps {
  children: React.ReactNode[];
  delay?: number;
  staggerDelay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  className?: string;
}

/**
 * Staggered reveal for multiple elements
 */
export const StaggeredReveal = ({
  children,
  delay = 0,
  staggerDelay = 100, // Reduced from 150ms to 100ms for faster stagger
  direction = 'up',
  className = ''
}: StaggeredRevealProps) => {
  return (
    <div className={className}>
      {children.map((child, index) => (
        <ScrollReveal
          key={index}
          delay={delay + (index * staggerDelay)}
          direction={direction}
          duration={500} // Reduced from 800ms to 500ms
        >
          {child}
        </ScrollReveal>
      ))}
    </div>
  );
};

interface ParallaxProps {
  children: React.ReactNode;
  speed?: number;
  className?: string;
}

/**
 * Enhanced parallax effect with performance optimization
 */
export const Parallax = ({
  children,
  speed = 0.5,
  className = ''
}: ParallaxProps) => {
  const [offset, setOffset] = useState(0);
  const [isInView, setIsInView] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);
  const rafId = useRef<number>();

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // Intersection Observer for performance
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsInView(entry.isIntersecting);
      },
      { rootMargin: '100px' }
    );

    observer.observe(element);

    const handleScroll = () => {
      if (!isInView) return;

      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }

      rafId.current = requestAnimationFrame(() => {
        if (!element) return;

        const rect = element.getBoundingClientRect();
        const scrolled = window.pageYOffset;
        const rate = scrolled * -speed;

        setOffset(rate);
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      observer.unobserve(element);
      window.removeEventListener('scroll', handleScroll);
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
    };
  }, [speed, isInView]);

  return (
    <div
      ref={elementRef}
      className={className}
      style={{
        transform: `translate3d(0, ${offset}px, 0)`,
        willChange: isInView ? 'transform' : 'auto'
      }}
    >
      {children}
    </div>
  );
};

interface CinematicRevealProps {
  children: React.ReactNode;
  delay?: number;
  animationType?: 'cinematic' | 'sequential' | 'choreography';
  className?: string;
}

/**
 * Advanced cinematic reveal component inspired by premium websites
 * Features sophisticated animations with blur, scale, and rotation effects
 */
export const CinematicReveal = ({
  children,
  delay = 0,
  animationType = 'cinematic',
  className = ''
}: CinematicRevealProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            setIsVisible(true);
          }, delay);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -100px 0px'
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [delay]);

  const getAnimationClass = () => {
    switch (animationType) {
      case 'sequential':
        return 'animate-sequential-fade';
      case 'choreography':
        return 'animate-element-choreography';
      default:
        return 'animate-cinematic-reveal';
    }
  };

  return (
    <div
      ref={elementRef}
      className={`${className} ${isVisible ? getAnimationClass() : ''}`}
      style={{
        opacity: isVisible ? 1 : 0,
        willChange: 'transform, opacity, filter'
      }}
    >
      {children}
    </div>
  );
};
