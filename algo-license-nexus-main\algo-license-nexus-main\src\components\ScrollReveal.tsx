import { useEffect, useRef, useState } from 'react';

// Global set to track triggered animations
const triggeredAnimations = new Set<string>();

interface ScrollRevealProps {
  children: React.ReactNode;
  delay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  distance?: number;
  duration?: number;
  className?: string;
  id?: string; // Optional unique ID for tracking
}

/**
 * Simple, reliable scroll reveal component
 * Guaranteed to work with visible animations
 */
export const ScrollReveal = ({
  children,
  delay = 0,
  direction = 'up',
  distance = 50,
  duration = 500, // Reduced from 800ms to 500ms for faster animations
  className = '',
  id
}: ScrollRevealProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  // Generate unique ID if not provided
  const animationId = useRef(id || `scroll-reveal-${Math.random().toString(36).substr(2, 9)}`);
  const hasTriggered = triggeredAnimations.has(animationId.current);

  useEffect(() => {
    const element = elementRef.current;
    if (!element || hasTriggered) {
      if (hasTriggered) {
        setIsVisible(true); // Show immediately if already triggered
      }
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !triggeredAnimations.has(animationId.current)) {
          console.log('ScrollReveal triggering for:', animationId.current);
          setTimeout(() => {
            setIsVisible(true);
            triggeredAnimations.add(animationId.current); // Global tracking
            console.log('ScrollReveal completed for:', animationId.current);
          }, delay);
        } else if (entry.isIntersecting && triggeredAnimations.has(animationId.current)) {
          console.log('ScrollReveal already triggered for:', animationId.current, '- preventing re-trigger');
        }
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [delay, hasTriggered]);

  const getTransform = () => {
    if (isVisible) return 'translate3d(0, 0, 0) scale(1)';

    switch (direction) {
      case 'up':
        return `translate3d(0, ${distance}px, 0) scale(0.95)`;
      case 'down':
        return `translate3d(0, -${distance}px, 0) scale(0.95)`;
      case 'left':
        return `translate3d(${distance}px, 0, 0) scale(0.95)`;
      case 'right':
        return `translate3d(-${distance}px, 0, 0) scale(0.95)`;
      default:
        return `translate3d(0, ${distance}px, 0) scale(0.95)`;
    }
  };

  return (
    <div
      ref={elementRef}
      className={className}
      style={{
        opacity: isVisible ? 1 : 0,
        transform: getTransform(),
        transition: `all ${duration}ms cubic-bezier(0.16, 1, 0.3, 1)`,
        willChange: 'transform, opacity'
      }}
    >
      {children}
    </div>
  );
};

interface StaggeredRevealProps {
  children: React.ReactNode[];
  delay?: number;
  staggerDelay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  className?: string;
}

/**
 * Staggered reveal for multiple elements
 */
export const StaggeredReveal = ({
  children,
  delay = 0,
  staggerDelay = 100, // Reduced from 150ms to 100ms for faster stagger
  direction = 'up',
  className = ''
}: StaggeredRevealProps) => {
  return (
    <div className={className}>
      {children.map((child, index) => (
        <ScrollReveal
          key={index}
          delay={delay + (index * staggerDelay)}
          direction={direction}
          duration={500} // Reduced from 800ms to 500ms
        >
          {child}
        </ScrollReveal>
      ))}
    </div>
  );
};

interface ParallaxProps {
  children: React.ReactNode;
  speed?: number;
  className?: string;
}

/**
 * Enhanced parallax effect with performance optimization
 */
export const Parallax = ({
  children,
  speed = 0.5,
  className = ''
}: ParallaxProps) => {
  const [offset, setOffset] = useState(0);
  const [isInView, setIsInView] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);
  const rafId = useRef<number>();

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // Intersection Observer for performance - no re-triggering needed for parallax
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsInView(entry.isIntersecting);
      },
      { rootMargin: '100px' }
    );

    observer.observe(element);

    const handleScroll = () => {
      if (!isInView) return;

      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }

      rafId.current = requestAnimationFrame(() => {
        if (!element) return;

        const rect = element.getBoundingClientRect();
        const scrolled = window.pageYOffset;
        const rate = scrolled * -speed;

        setOffset(rate);
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      observer.unobserve(element);
      window.removeEventListener('scroll', handleScroll);
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
    };
  }, [speed, isInView]);

  return (
    <div
      ref={elementRef}
      className={className}
      style={{
        transform: `translate3d(0, ${offset}px, 0)`,
        willChange: isInView ? 'transform' : 'auto'
      }}
    >
      {children}
    </div>
  );
};

interface CinematicRevealProps {
  children: React.ReactNode;
  delay?: number;
  animationType?: 'cinematic' | 'sequential' | 'choreography';
  className?: string;
  id?: string; // Optional unique ID for tracking
}

/**
 * Advanced cinematic reveal component inspired by premium websites
 * Features sophisticated animations with blur, scale, and rotation effects
 */
export const CinematicReveal = ({
  children,
  delay = 0,
  animationType = 'cinematic',
  className = '',
  id
}: CinematicRevealProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  // Generate unique ID if not provided
  const animationId = useRef(id || `cinematic-reveal-${Math.random().toString(36).substr(2, 9)}`);
  const hasTriggered = triggeredAnimations.has(animationId.current);

  useEffect(() => {
    const element = elementRef.current;
    if (!element || hasTriggered) {
      if (hasTriggered) {
        setIsVisible(true); // Show immediately if already triggered
      }
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !triggeredAnimations.has(animationId.current)) {
          setTimeout(() => {
            setIsVisible(true);
            triggeredAnimations.add(animationId.current); // Global tracking
          }, delay);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -100px 0px'
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [delay, hasTriggered]);

  const getAnimationClass = () => {
    switch (animationType) {
      case 'sequential':
        return 'animate-sequential-fade';
      case 'choreography':
        return 'animate-element-choreography';
      default:
        return 'animate-cinematic-reveal';
    }
  };

  return (
    <div
      ref={elementRef}
      className={`${className} ${isVisible ? getAnimationClass() : ''}`}
      style={{
        opacity: isVisible ? 1 : 0,
        willChange: 'transform, opacity, filter'
      }}
    >
      {children}
    </div>
  );
};
