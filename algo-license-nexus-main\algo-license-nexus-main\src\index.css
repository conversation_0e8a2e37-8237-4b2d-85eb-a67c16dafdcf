@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Modern Design System Variables */
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --premium-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --accent-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    scroll-behavior: smooth;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* High Contrast Glass Effects for Better Readability */
  .glass {
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.25);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.6),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .glass-strong {
    background: rgba(15, 23, 42, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow:
      0 16px 48px rgba(0, 0, 0, 0.7),
      inset 0 1px 0 rgba(255, 255, 255, 0.15),
      inset 0 -1px 0 rgba(255, 255, 255, 0.05);
  }

  .glass-subtle {
    background: rgba(15, 23, 42, 0.92);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.08);
  }

  /* Modern Shadows */
  .shadow-modern {
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .shadow-modern-lg {
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .shadow-glow {
    box-shadow:
      0 0 20px rgba(59, 130, 246, 0.3),
      0 0 40px rgba(59, 130, 246, 0.1);
  }

  /* Modern Gradients */
  .gradient-premium {
    background: var(--premium-gradient);
  }

  .gradient-accent {
    background: var(--accent-gradient);
  }

  .gradient-success {
    background: var(--success-gradient);
  }

  /* Modern Typography */
  .text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-blue {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Subtle Modern Animations */
  .animate-float {
    animation: float 8s ease-in-out infinite;
  }

  .animate-float-slow {
    animation: float 12s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 3s ease-in-out infinite alternate;
  }

  .animate-glow-subtle {
    animation: glowSubtle 4s ease-in-out infinite alternate;
  }

  .animate-slide-up {
    animation: slideUp 0.8s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .animate-fade-in {
    animation: fadeIn 1s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .animate-scale-in {
    animation: scaleIn 0.7s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .animate-breathe {
    animation: breathe 4s ease-in-out infinite;
  }

  .animate-pulse-subtle {
    animation: pulseSubtle 4s ease-in-out infinite;
  }

  .animate-drift {
    animation: drift 20s ease-in-out infinite;
  }

  /* Cinematic Animation Classes */
  .animate-section-reveal {
    animation: sectionReveal 1.4s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  .animate-hero-entrance {
    animation: heroEntrance 2s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  .animate-card-cascade {
    animation: cardCascade 1.2s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  .animate-text-reveal {
    animation: textReveal 1s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  .animate-progressive-blur {
    animation: progressiveBlur 1.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  .animate-parallax-slow {
    animation: parallaxSlow 30s linear infinite;
  }

  .animate-parallax-medium {
    animation: parallaxMedium 20s linear infinite;
  }

  .animate-parallax-fast {
    animation: parallaxFast 15s linear infinite;
  }

  /* Enhanced Cinematic Animations */
  .animate-cinematic-reveal {
    animation: cinematicReveal 2s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  .animate-sequential-fade {
    animation: sequentialFade 1.8s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  .animate-parallax-float {
    animation: parallaxFloat 8s ease-in-out infinite;
  }

  .animate-scale-breathe {
    animation: scaleBreath 6s ease-in-out infinite;
  }

  .animate-text-shimmer {
    animation: textShimmer 3s ease-in-out infinite;
  }

  .animate-element-choreography {
    animation: elementChoreography 2.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  /* Background utilities */
  .bg-size-200 {
    background-size: 200% 200%;
  }

  .bg-pos-0 {
    background-position: 0% 50%;
  }

  .bg-pos-100 {
    background-position: 100% 50%;
  }

  /* Subtle Interaction Effects */
  .hover-lift {
    transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .hover-lift:hover {
    transform: translateY(-2px);
  }

  .hover-glow {
    transition: box-shadow 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
  }

  .hover-scale {
    transition: transform 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .hover-scale:hover {
    transform: scale(1.02);
  }

  /* Typography Enhancements */
  .text-balance {
    text-wrap: balance;
  }

  .letter-spacing-tight {
    letter-spacing: -0.02em;
  }

  .letter-spacing-wide {
    letter-spacing: 0.05em;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-12px); }
}

@keyframes glow {
  from { box-shadow: 0 0 20px rgba(59, 130, 246, 0.2); }
  to { box-shadow: 0 0 25px rgba(59, 130, 246, 0.4); }
}

@keyframes glowSubtle {
  from { box-shadow: 0 0 10px rgba(59, 130, 246, 0.1); }
  to { box-shadow: 0 0 15px rgba(59, 130, 246, 0.2); }
}

@keyframes breathe {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.02); opacity: 1; }
}

@keyframes pulseSubtle {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 0.85; }
}

@keyframes drift {
  0%, 100% { transform: translateX(0px) translateY(0px); }
  25% { transform: translateX(10px) translateY(-5px); }
  50% { transform: translateX(-5px) translateY(-10px); }
  75% { transform: translateX(-10px) translateY(5px); }
}

/* Cinematic Keyframes */
@keyframes sectionReveal {
  0% {
    opacity: 0;
    transform: translateY(80px) scale(0.95);
    filter: blur(10px);
  }
  50% {
    opacity: 0.7;
    transform: translateY(20px) scale(0.98);
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) scale(1);
    filter: blur(0px);
  }
}

@keyframes heroEntrance {
  0% {
    opacity: 0;
    transform: translateY(100px) scale(0.9);
    filter: blur(20px);
  }
  30% {
    opacity: 0.3;
    transform: translateY(50px) scale(0.95);
    filter: blur(10px);
  }
  70% {
    opacity: 0.8;
    transform: translateY(10px) scale(0.99);
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) scale(1);
    filter: blur(0px);
  }
}

@keyframes cardCascade {
  0% {
    opacity: 0;
    transform: translateY(60px) rotateX(15deg);
    filter: blur(5px);
  }
  60% {
    opacity: 0.8;
    transform: translateY(10px) rotateX(3deg);
    filter: blur(1px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) rotateX(0deg);
    filter: blur(0px);
  }
}

@keyframes textReveal {
  0% {
    opacity: 0;
    transform: translateY(30px);
    filter: blur(3px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
    filter: blur(0px);
  }
}

@keyframes progressiveBlur {
  0% {
    opacity: 0;
    filter: blur(15px) brightness(0.5);
  }
  50% {
    opacity: 0.7;
    filter: blur(5px) brightness(0.8);
  }
  100% {
    opacity: 1;
    filter: blur(0px) brightness(1);
  }
}

@keyframes parallaxSlow {
  0% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
  100% { transform: translateY(0px) rotate(360deg); }
}

@keyframes parallaxMedium {
  0% { transform: translateY(0px) translateX(0px); }
  25% { transform: translateY(-15px) translateX(10px); }
  50% { transform: translateY(-30px) translateX(0px); }
  75% { transform: translateY(-15px) translateX(-10px); }
  100% { transform: translateY(0px) translateX(0px); }
}

@keyframes parallaxFast {
  0% { transform: translateY(0px) scale(1); }
  33% { transform: translateY(-10px) scale(1.02); }
  66% { transform: translateY(-20px) scale(0.98); }
  100% { transform: translateY(0px) scale(1); }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Enhanced Cinematic Keyframes */
@keyframes cinematicReveal {
  0% {
    opacity: 0;
    transform: translateY(120px) scale(0.9) rotateX(15deg);
    filter: blur(20px) brightness(0.3);
  }
  25% {
    opacity: 0.3;
    transform: translateY(60px) scale(0.95) rotateX(8deg);
    filter: blur(10px) brightness(0.6);
  }
  60% {
    opacity: 0.8;
    transform: translateY(15px) scale(0.99) rotateX(2deg);
    filter: blur(2px) brightness(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) scale(1) rotateX(0deg);
    filter: blur(0px) brightness(1);
  }
}

@keyframes sequentialFade {
  0% {
    opacity: 0;
    transform: translateY(60px) translateX(-30px) scale(0.95);
  }
  40% {
    opacity: 0.6;
    transform: translateY(20px) translateX(-10px) scale(0.98);
  }
  70% {
    opacity: 0.9;
    transform: translateY(5px) translateX(-2px) scale(0.995);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) translateX(0px) scale(1);
  }
}

@keyframes parallaxFloat {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-15px) translateX(8px) rotate(1deg);
  }
  50% {
    transform: translateY(-25px) translateX(0px) rotate(0deg);
  }
  75% {
    transform: translateY(-15px) translateX(-8px) rotate(-1deg);
  }
}

@keyframes scaleBreath {
  0%, 100% {
    transform: scale(1) translateZ(0);
  }
  50% {
    transform: scale(1.03) translateZ(0);
  }
}

@keyframes textShimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes elementChoreography {
  0% {
    opacity: 0;
    transform: translateY(100px) scale(0.8) rotateY(15deg);
    filter: blur(15px);
  }
  20% {
    opacity: 0.4;
    transform: translateY(60px) scale(0.9) rotateY(8deg);
    filter: blur(8px);
  }
  50% {
    opacity: 0.8;
    transform: translateY(20px) scale(0.97) rotateY(3deg);
    filter: blur(3px);
  }
  80% {
    opacity: 0.95;
    transform: translateY(5px) scale(0.99) rotateY(1deg);
    filter: blur(1px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px) scale(1) rotateY(0deg);
    filter: blur(0px);
  }
}

/* Smooth Scrolling */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}