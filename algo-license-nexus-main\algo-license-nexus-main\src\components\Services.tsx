
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollReveal, StaggeredReveal, CinematicReveal } from "@/components/ScrollReveal";
import { useCinematicScroll } from "@/hooks/useScrollAnimation";
import { TrendingUp, Target, Database, Cpu } from "lucide-react";

export const Services = () => {
  // Cinematic scroll effects for background elements
  const backgroundEffect = useCinematicScroll({
    parallaxSpeed: 0.2,
    enableScale: true,
    scaleRange: [1, 1.05],
    enableParallax: true
  });

  const strategies = [
    {
      icon: TrendingUp,
      title: "Quantum Trading Algorithms",
      description: "Revolutionary quantum-inspired trading strategies with 340% average ROI",
      features: ["Real-time market analysis", "Risk optimization", "Predictive modeling"],
      exclusivity: "Tier 1",
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: Target,
      title: "Neural Prediction Engine",
      description: "Advanced neural networks for market forecasting and trend prediction",
      features: ["Deep learning models", "Pattern recognition", "Sentiment analysis"],
      exclusivity: "Tier 1",
      color: "from-purple-500 to-pink-500"
    },
    {
      icon: Database,
      title: "Adaptive Portfolio Optimizer",
      description: "Self-learning portfolio management with dynamic risk adjustment",
      features: ["Portfolio balancing", "Risk assessment", "Performance tracking"],
      exclusivity: "Tier 2",
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: Cpu,
      title: "Multi-Asset Strategy Suite",
      description: "Comprehensive algorithmic strategies for diverse asset classes",
      features: ["Cross-market analysis", "Arbitrage detection", "Liquidity optimization"],
      exclusivity: "Tier 2",
      color: "from-orange-500 to-red-500"
    }
  ];

  return (
    <section className="py-24 px-6 relative overflow-hidden">
      {/* Enhanced Background with Cinematic Effects */}
      <div ref={backgroundEffect.elementRef} style={backgroundEffect.cinematicStyle} className="absolute inset-0 opacity-30">
        <div className="absolute w-[600px] h-[600px] bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full blur-3xl top-1/4 left-1/4 animate-parallax-float" />
        <div className="absolute w-[400px] h-[400px] bg-gradient-to-r from-cyan-500/10 to-blue-500/10 rounded-full blur-2xl bottom-1/4 right-1/4 animate-scale-breathe" />
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        <CinematicReveal delay={200} animationType="cinematic">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Premium AI <span className="bg-gradient-to-r from-blue-400 via-cyan-400 to-purple-400 bg-clip-text text-transparent animate-text-shimmer bg-size-200">Strategy Portfolio</span>
            </h2>
            <p className="text-xl text-gray-100 max-w-3xl mx-auto font-medium">
              Our exclusive algorithmic strategies are the result of years of research and development by leading AI scientists and quantitative analysts.
            </p>
          </div>
        </CinematicReveal>

        <StaggeredReveal
          delay={600}
          staggerDelay={200}
          className="grid grid-cols-1 md:grid-cols-2 gap-8"
        >
          {strategies.map((strategy, index) => (
            <Card
              key={index}
              className="group bg-slate-800 border-white/30 hover:border-white/40 transition-all duration-500 hover-lift hover-glow"
            >
              <CardHeader className="relative">
                <div className="flex items-center justify-between mb-6">
                  <div className={`relative p-4 rounded-2xl bg-gradient-to-br ${strategy.color} shadow-glow-subtle group-hover:scale-105 transition-transform duration-300`}>
                    <strategy.icon className="w-9 h-9 text-white group-hover:animate-pulse-subtle" />
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/15 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                  <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black font-bold px-3 py-1 rounded-full shadow-glow">
                    {strategy.exclusivity}
                  </Badge>
                </div>
                <CardTitle className="text-2xl font-bold text-white group-hover:text-gradient-blue transition-all duration-300 mb-3">
                  {strategy.title}
                </CardTitle>
                <CardDescription className="text-gray-100 text-lg leading-relaxed group-hover:text-white transition-colors duration-300 font-medium">
                  {strategy.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ul className="space-y-3">
                  {strategy.features.map((feature, idx) => (
                    <li
                      key={idx}
                      className="flex items-start text-gray-100 group-hover:text-white transition-colors duration-300 animate-slide-up"
                      style={{ animationDelay: `${(index * 0.2) + (idx * 0.1)}s` }}
                    >
                      <div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full mr-3 mt-2 flex-shrink-0 group-hover:shadow-glow transition-all duration-300"></div>
                      <span className="leading-relaxed font-medium">{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* Performance indicator */}
                <div className="mt-6 p-4 bg-slate-700/50 rounded-xl border border-white/20">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-200 font-medium">Performance Rating</span>
                    <div className="flex items-center space-x-1">
                      {[...Array(5)].map((_, i) => (
                        <div
                          key={i}
                          className={`w-2 h-2 rounded-full ${i < 4 ? 'bg-gradient-to-r from-blue-400 to-purple-400' : 'bg-gray-600'} group-hover:shadow-glow transition-all duration-300`}
                          style={{ animationDelay: `${i * 0.1}s` }}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </StaggeredReveal>

        <ScrollReveal delay={1000} direction="up">
          <div className="text-center mt-16">
            <p className="text-gray-200 text-lg font-medium">
              All strategies include comprehensive documentation, implementation support, and ongoing updates.
            </p>
          </div>
        </ScrollReveal>
      </div>
    </section>
  );
};
