import { useEffect, useRef, useState, useCallback } from 'react';

interface UseScrollAnimationOptions {
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
  delay?: number;
  duration?: number;
  enableParallax?: boolean;
  parallaxSpeed?: number;
  cinematicTiming?: boolean;
}

interface ScrollProgress {
  progress: number;
  direction: 'up' | 'down';
  velocity: number;
  scrollY: number;
  elementTop: number;
  elementHeight: number;
  viewportHeight: number;
}

interface CinematicTimingConfig {
  ease: string;
  staggerDelay: number;
  revealDistance: number;
  scaleEffect: number;
  blurEffect: number;
}

/**
 * Advanced scroll animation hook with cinematic capabilities
 * Provides smooth, performance-optimized scroll animations
 * Uses Intersection Observer and RAF for efficient scroll detection
 * Enhanced with parallax effects and cinematic timing
 */
export const useScrollAnimation = (options: UseScrollAnimationOptions = {}) => {
  const {
    threshold = 0.1,
    rootMargin = '0px 0px -100px 0px',
    triggerOnce = true,
    delay = 0,
    duration = 800,
    enableParallax = false,
    parallaxSpeed = 0.5,
    cinematicTiming = false
  } = options;

  const [isVisible, setIsVisible] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);
  const [scrollProgress, setScrollProgress] = useState<ScrollProgress>({
    progress: 0,
    direction: 'down',
    velocity: 0,
    scrollY: 0,
    elementTop: 0,
    elementHeight: 0,
    viewportHeight: 0
  });

  const elementRef = useRef<HTMLElement>(null);
  const lastScrollY = useRef(0);
  const lastTimestamp = useRef(0);

  // Respect user's motion preferences
  const prefersReducedMotion = useRef(
    typeof window !== 'undefined' &&
    window.matchMedia('(prefers-reduced-motion: reduce)').matches
  );

  const updateScrollProgress = useCallback(() => {
    if (!elementRef.current) return;

    const element = elementRef.current;
    const rect = element.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    const currentScrollY = window.scrollY;

    // Calculate progress (0 to 1) as element moves through viewport
    const elementTop = rect.top;
    const elementHeight = rect.height;
    const progress = Math.max(0, Math.min(1,
      (windowHeight - elementTop) / (windowHeight + elementHeight)
    ));

    // Calculate scroll direction and velocity for cinematic effects
    const currentTime = performance.now();
    const deltaY = currentScrollY - lastScrollY.current;
    const deltaTime = currentTime - lastTimestamp.current;
    const velocity = deltaTime > 0 ? Math.abs(deltaY / deltaTime) : 0;

    setScrollProgress({
      progress,
      direction: deltaY > 0 ? 'down' : 'up',
      velocity: Math.min(velocity, 2), // Cap velocity for smooth animations
      scrollY: currentScrollY,
      elementTop: elementTop + currentScrollY, // Absolute position
      elementHeight,
      viewportHeight: windowHeight
    });

    lastScrollY.current = currentScrollY;
    lastTimestamp.current = currentTime;
  }, []);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        const isIntersecting = entry.isIntersecting;

        if (isIntersecting && (!triggerOnce || !hasTriggered)) {
          // Apply delay if specified
          const animationDelay = prefersReducedMotion.current ? 0 : delay;

          setTimeout(() => {
            setIsVisible(true);
            if (triggerOnce) {
              setHasTriggered(true);
            }
          }, animationDelay);
        } else if (!triggerOnce && !isIntersecting) {
          setIsVisible(false);
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    observer.observe(element);

    // Add scroll listener for progress tracking
    let ticking = false;
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          updateScrollProgress();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      observer.unobserve(element);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [threshold, rootMargin, triggerOnce, hasTriggered, delay, updateScrollProgress]);

  return {
    elementRef,
    isVisible,
    hasTriggered,
    scrollProgress,
    prefersReducedMotion: prefersReducedMotion.current,
    animationDuration: prefersReducedMotion.current ? 0 : duration
  };
};

/**
 * Hook for staggered animations with cinematic timing
 * Creates coordinated delays for multiple elements
 */
export const useStaggeredAnimation = (
  itemCount: number,
  baseDelay: number = 150,
  options: UseScrollAnimationOptions = {}
) => {
  const { elementRef, isVisible, prefersReducedMotion, scrollProgress } = useScrollAnimation(options);

  const getItemDelay = (index: number) => {
    if (prefersReducedMotion) return 0;
    return isVisible ? index * baseDelay : 0;
  };

  const getItemStyle = (index: number) => ({
    animationDelay: `${getItemDelay(index)}ms`,
    animationFillMode: 'both' as const,
    animationDuration: prefersReducedMotion ? '0ms' : '800ms',
  });

  const getProgressiveReveal = (index: number, totalItems: number) => {
    const itemProgress = Math.max(0, Math.min(1,
      (scrollProgress.progress * totalItems) - index
    ));
    return {
      opacity: itemProgress,
      transform: `translateY(${(1 - itemProgress) * 30}px)`,
      transition: prefersReducedMotion ? 'none' : 'all 0.6s cubic-bezier(0.16, 1, 0.3, 1)'
    };
  };

  return {
    elementRef,
    isVisible,
    getItemDelay,
    getItemStyle,
    getProgressiveReveal,
    scrollProgress
  };
};

/**
 * Advanced parallax hook with multiple layers and performance optimization
 */
export const useParallax = (speed: number = 0.5, options: {
  enableOnMobile?: boolean;
  maxOffset?: number;
} = {}) => {
  const { enableOnMobile = false, maxOffset = 200 } = options;
  const [offset, setOffset] = useState(0);
  const [isInView, setIsInView] = useState(false);
  const elementRef = useRef<HTMLElement>(null);
  const rafId = useRef<number>();

  // Check if device supports parallax
  const supportsParallax = useRef(
    typeof window !== 'undefined' &&
    (enableOnMobile || window.innerWidth > 768) &&
    !window.matchMedia('(prefers-reduced-motion: reduce)').matches
  );

  useEffect(() => {
    if (!supportsParallax.current) return;

    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsInView(entry.isIntersecting);
      },
      { rootMargin: '100px' }
    );

    observer.observe(element);

    const updateParallax = () => {
      if (!isInView || !element) return;

      const rect = element.getBoundingClientRect();
      const scrolled = window.pageYOffset;
      const elementTop = rect.top + scrolled;
      const windowHeight = window.innerHeight;
      const elementHeight = rect.height;

      // Calculate parallax offset based on element position in viewport
      const scrollProgress = (scrolled + windowHeight - elementTop) / (windowHeight + elementHeight);
      const parallaxOffset = Math.max(-maxOffset, Math.min(maxOffset,
        (scrollProgress - 0.5) * speed * 100
      ));

      setOffset(parallaxOffset);
    };

    const handleScroll = () => {
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
      rafId.current = requestAnimationFrame(updateParallax);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      observer.unobserve(element);
      window.removeEventListener('scroll', handleScroll);
      if (rafId.current) {
        cancelAnimationFrame(rafId.current);
      }
    };
  }, [speed, maxOffset, isInView]);

  return {
    elementRef,
    offset,
    isInView,
    style: supportsParallax.current ? {
      transform: `translate3d(0, ${offset}px, 0)`,
      willChange: isInView ? 'transform' : 'auto'
    } : {}
  };
};

/**
 * Hook for section-based reveal animations
 * Creates cinematic section transitions
 */
export const useSectionReveal = (sectionId: string, delay: number = 0) => {
  const { elementRef, isVisible, scrollProgress, prefersReducedMotion } = useScrollAnimation({
    threshold: 0.2,
    rootMargin: '0px 0px -150px 0px',
    delay,
    triggerOnce: true
  });

  const sectionStyle = {
    opacity: isVisible ? 1 : 0,
    transform: isVisible
      ? 'translateY(0px) scale(1)'
      : 'translateY(60px) scale(0.95)',
    transition: prefersReducedMotion
      ? 'none'
      : 'all 1.2s cubic-bezier(0.16, 1, 0.3, 1)',
    transitionDelay: prefersReducedMotion ? '0ms' : `${delay}ms`
  };

  return {
    elementRef,
    isVisible,
    scrollProgress,
    sectionStyle,
    prefersReducedMotion
  };
};

/**
 * Advanced cinematic scroll hook inspired by premium websites
 * Provides sophisticated animations with parallax, blur, and scale effects
 */
export const useCinematicScroll = (options: {
  parallaxSpeed?: number;
  blurIntensity?: number;
  scaleRange?: [number, number];
  rotationRange?: [number, number];
  enableParallax?: boolean;
  enableBlur?: boolean;
  enableScale?: boolean;
  enableRotation?: boolean;
} = {}) => {
  const {
    parallaxSpeed = 0.5,
    blurIntensity = 10,
    scaleRange = [0.95, 1.05],
    rotationRange = [-2, 2],
    enableParallax = true,
    enableBlur = false,
    enableScale = true,
    enableRotation = false
  } = options;

  const { elementRef, scrollProgress, isVisible, prefersReducedMotion } = useScrollAnimation({
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px',
    triggerOnce: false
  });

  const cinematicStyle = {
    transform: (() => {
      if (prefersReducedMotion) return 'none';

      const transforms = [];

      // Parallax effect
      if (enableParallax) {
        const parallaxY = scrollProgress.progress * parallaxSpeed * 100;
        transforms.push(`translateY(${parallaxY}px)`);
      }

      // Scale effect
      if (enableScale) {
        const scale = scaleRange[0] + (scrollProgress.progress * (scaleRange[1] - scaleRange[0]));
        transforms.push(`scale(${scale})`);
      }

      // Rotation effect
      if (enableRotation) {
        const rotation = rotationRange[0] + (scrollProgress.progress * (rotationRange[1] - rotationRange[0]));
        transforms.push(`rotate(${rotation}deg)`);
      }

      // Hardware acceleration
      transforms.push('translateZ(0)');

      return transforms.join(' ');
    })(),
    filter: (() => {
      if (prefersReducedMotion || !enableBlur) return 'none';
      const blur = (1 - scrollProgress.progress) * blurIntensity;
      return `blur(${Math.max(0, blur)}px)`;
    })(),
    opacity: isVisible ? 1 : 0,
    transition: prefersReducedMotion ? 'none' : 'opacity 0.8s cubic-bezier(0.16, 1, 0.3, 1)',
    willChange: prefersReducedMotion ? 'auto' : 'transform, filter, opacity'
  };

  return {
    elementRef,
    cinematicStyle,
    scrollProgress,
    isVisible,
    prefersReducedMotion
  };
};
